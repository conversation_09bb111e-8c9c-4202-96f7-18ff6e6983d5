import base64
import builtins
import contextlib
import io
import subprocess
import tempfile
import os
from typing import Any, Optional, Sequence, Union, Callable
import asyncio
import inspect
import json

from langchain_google_genai import ChatGoogleGenerativeAI
from langchain_core.runnables import RunnableConfig
from langgraph.checkpoint.memory import MemorySaver
from langchain_mcp_adapters.client import MultiServerMCPClient
from langchain_core.tools import StructuredTool, tool as create_tool

from langgraph_codeact import create_codeact
from dotenv import load_dotenv
load_dotenv()

def custom_create_default_prompt(tools: list[Union[StructuredTool, Callable]], base_prompt: Optional[str] = None):
    """Create default prompt for the CodeAct agent, handling StructuredTool."""
    tools = [t if isinstance(t, StructuredTool) else create_tool(t) for t in tools]
    prompt = f"{base_prompt}\n\n" if base_prompt else ""
    prompt += """You will be given a task to perform. You should output either
- a Python code snippet that provides the solution to the task, or a step towards the solution. Any output you want to extract from the code should be printed to the console. Code should be output in a fenced code block.
- text to be shown directly to the user, if you want to ask for more information or provide the final answer.

You have access to a full conda environment with pre-installed packages including:
- Data Science: numpy, pandas, matplotlib, seaborn, scipy, scikit-learn, plotly
- Machine Learning: torch, tensorflow, transformers
- Web: requests, beautifulsoup4, fastapi, aiohttp
- NLP: nltk, spacy, gensim
- And many more scientific and development packages

In addition to the conda environment, you can use the following functions:
"""

    for tool in tools:
        if isinstance(tool, StructuredTool):
            params = []
            if tool.args_schema:
                if hasattr(tool.args_schema, 'model_json_schema') and callable(tool.args_schema.model_json_schema):
                    schema_props = tool.args_schema.model_json_schema().get('properties', {})
                elif isinstance(tool.args_schema, dict):
                    schema_props = tool.args_schema.get('properties', {})
                else:
                    schema_props = {}

                for name, prop in schema_props.items():
                    param_type = prop.get('type', 'Any')
                    if 'format' in prop and prop['format'] == 'uri':
                        param_type = 'str'
                    params.append(f"{name}: {param_type}")
            signature = f"({', '.join(params)})"
        else:
            signature = str(inspect.signature(tool))

        prompt += f'''
def {tool.name}{signature}:
    """{tool.description}"""
    ...
'''

    prompt += """

Variables defined at the top level of previous code snippets can be referenced in your code.

Reminder: use Python code snippets to call tools. All code runs in a pre-configured conda environment with scientific packages."""
    return prompt

def eval_with_conda(code: str, _locals: dict[str, Any]) -> tuple[str, dict[str, Any]]:
    """Execute code in conda environment and return results."""
    conda_env_path = os.getenv("CODEACT_CONDA_ENV_PATH", "/opt/conda/envs/codeact")
    conda_python = f"{conda_env_path}/bin/python"
    
    # If conda environment doesn't exist, fall back to regular eval
    if not os.path.exists(conda_python):
        return eval_fallback(code, _locals)
    
    # Store original keys before execution
    original_keys = set(_locals.keys())
    
    try:
        # Create a temporary file with the code and variables
        with tempfile.NamedTemporaryFile(mode='w', suffix='.py', delete=False) as f:
            # Import serialized locals
            f.write("import json\n")
            f.write("import sys\n")
            f.write("import io\n")
            f.write("from contextlib import redirect_stdout\n\n")
            
            # Deserialize previous variables
            serializable_locals = {}
            for key, value in _locals.items():
                try:
                    json.dumps(value, default=str)
                    serializable_locals[key] = value
                except (TypeError, ValueError):
                    pass
            
            f.write(f"_imported_locals = {repr(serializable_locals)}\n")
            f.write("locals().update(_imported_locals)\n\n")
            
            # Add the user code
            f.write("# User code starts here\n")
            f.write("with redirect_stdout(io.StringIO()) as f:\n")
            for line in code.split('\n'):
                f.write(f"    {line}\n")
            f.write("\n")
            
            # Export variables and output
            f.write("output = f.getvalue()\n")
            f.write("if not output:\n")
            f.write("    output = '<code ran, no output printed to stdout>'\n")
            f.write("print('OUTPUT_START')\n")
            f.write("print(output)\n")
            f.write("print('OUTPUT_END')\n")
            
            # Export new variables
            f.write("new_vars = {}\n")
            f.write(f"original_keys = {repr(original_keys)}\n")
            f.write("for key, value in locals().items():\n")
            f.write("    if (key not in original_keys and \n")
            f.write("        not key.startswith('__') and \n")
            f.write("        key not in ['json', 'sys', 'io', 'redirect_stdout', 'f', 'output', 'new_vars', 'original_keys', '_imported_locals']):\n")
            f.write("        try:\n")
            f.write("            json.dumps(value, default=str)\n")
            f.write("            new_vars[key] = value\n")
            f.write("        except (TypeError, ValueError):\n")
            f.write("            pass\n")
            f.write("print('VARS_START')\n")
            f.write("print(json.dumps(new_vars, default=str))\n")
            f.write("print('VARS_END')\n")
            
            temp_file = f.name
        
        # Execute with conda python
        result = subprocess.run(
            [conda_python, temp_file],
            capture_output=True,
            text=True,
            timeout=30,
            cwd="/workspace"
        )
        
        # Clean up
        os.unlink(temp_file)
        
        if result.returncode != 0:
            return f"Error during execution: {result.stderr}", {}
        
        # Parse output
        stdout = result.stdout
        output_start = stdout.find('OUTPUT_START\n')
        output_end = stdout.find('\nOUTPUT_END')
        vars_start = stdout.find('VARS_START\n')
        vars_end = stdout.find('\nVARS_END')
        
        if output_start >= 0 and output_end >= 0:
            execution_output = stdout[output_start + len('OUTPUT_START\n'):output_end]
        else:
            execution_output = stdout.strip()
            
        new_vars = {}
        if vars_start >= 0 and vars_end >= 0:
            try:
                vars_json = stdout[vars_start + len('VARS_START\n'):vars_end]
                new_vars = json.loads(vars_json)
            except json.JSONDecodeError:
                pass
        
        # Update _locals with new variables
        _locals.update(new_vars)
        
        return execution_output, new_vars
        
    except subprocess.TimeoutExpired:
        return "Error: Code execution timed out (30s limit)", {}
    except Exception as e:
        return f"Error during conda execution: {str(e)}", {}
    finally:
        # Clean up temp file if it exists
        if 'temp_file' in locals() and os.path.exists(temp_file):
            os.unlink(temp_file)

def eval_fallback(code: str, _locals: dict[str, Any]) -> tuple[str, dict[str, Any]]:
    """Fallback eval function for when conda environment is not available."""
    original_keys = set(_locals.keys())

    try:
        with contextlib.redirect_stdout(io.StringIO()) as f:
            exec(code, builtins.__dict__, _locals)
        result = f.getvalue()
        if not result:
            result = "<code ran, no output printed to stdout>"
    except Exception as e:
        result = f"Error during execution: {repr(e)}"

    new_vars = {}
    for key, value in _locals.items():
        if (key not in original_keys and 
            not key.startswith('__') and 
            not inspect.ismodule(value) and 
            not inspect.isfunction(value) and 
            not inspect.isclass(value) and
            not callable(value)):
            try:
                json.dumps(value, default=str)
                new_vars[key] = value
            except (TypeError, ValueError):
                pass
    
    return result, new_vars

async def build_agent() -> tuple[Any, list]:
    """Build CodeAct agent with conda environment support."""
    # Configure MCP filesystem paths for containerized environment
    filesystem_path_override = os.getenv("CODEACT_MCP_FILESYSTEM_PATH", "/workspace")
    tmp_path_override = os.getenv("CODEACT_MCP_TMP_PATH", "/tmp")

    # Initialize MCP client
    client = MultiServerMCPClient(
        {
            "fetch": {"command": "uvx", "args": ["mcp-server-fetch"], "transport": "stdio"},
            "filesystem": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-filesystem", tmp_path_override, filesystem_path_override], "transport": "stdio"},
        }
    )
    
    mcp_tools = await client.get_tools()
    print(f"MCP Tools loaded: {len(mcp_tools)}")
    mcp_tools = [tool for tool in mcp_tools if tool is not None]

    # Merge all tools
    tools = [*mcp_tools]

    # Initialize model
    model = ChatGoogleGenerativeAI(
        model="gemini-1.5-flash-latest", 
        google_api_key=os.getenv("GOOGLE_API_KEY")
    )
    
    # Create CodeAct agent with conda evaluation
    code_act = create_codeact(
        model,
        tools,
        eval_with_conda,
        prompt=custom_create_default_prompt(
            tools,
            "You are a CodeAct agent running in a containerized conda environment with pre-installed scientific packages. "
            "Use the conda environment for all code execution. Once you have the final answer, respond to the user with plain text.",
        ),
    )
    
    # Compile with persistent checkpointing for conversation history
    agent = code_act.compile(checkpointer=MemorySaver())
    return agent, mcp_tools

# Export for use by A2A server
__all__ = ["build_agent"]

# Development testing
if __name__ == "__main__":
    agent, _ = asyncio.run(build_agent())

    def stream_from_agent(messages: list[dict], config: RunnableConfig):
        for typ, chunk in agent.stream(
            {"messages": messages},
            stream_mode=["values", "messages"],
            config=config,
        ):
            if typ == "messages":
                print(chunk[0].content, end="")
            elif typ == "values":
                print("\n\n---answer---\n\n", chunk)

    # Test conda environment
    config = {"configurable": {"thread_id": "test_conda"}}
    stream_from_agent([{
        "role": "user",
        "content": "Test the conda environment by importing numpy, pandas, and matplotlib. Create a simple plot and save it to a file."
    }], config)