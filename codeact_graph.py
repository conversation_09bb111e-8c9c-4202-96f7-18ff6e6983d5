import base64
import builtins
import contextlib
import io
from typing import Any
import asyncio
import inspect
from typing import Any, Optional, Sequence, Union, Callable

from langchain.chat_models import init_chat_model
from langchain_core.runnables import RunnableConfig
from langgraph.checkpoint.memory import MemorySaver
from langchain_mcp_adapters.client import MultiServerMCPClient
from langchain_core.tools import StructuredTool, tool as create_tool

from langgraph_codeact import create_codeact
from dotenv import load_dotenv
load_dotenv()

def custom_create_default_prompt(tools: list[Union[StructuredTool, Callable]], base_prompt: Optional[str] = None):
    """Create default prompt for the CodeAct agent, handling StructuredTool."""
    tools = [t if isinstance(t, StructuredTool) else create_tool(t) for t in tools]
    prompt = f"{base_prompt}\n\n" if base_prompt else ""
    prompt += """You will be given a task to perform. You should output either
- a Python code snippet that provides the solution to the task, or a step towards the solution. Any output you want to extract from the code should be printed to the console. Code should be output in a fenced code block.
- text to be shown directly to the user, if you want to ask for more information or provide the final answer.

In addition to the Python Standard Library, you have pre-imported:
- numpy (as np)
- pandas (as pd) 
- matplotlib.pyplot (as plt)

You can import additional packages if needed (scipy, sklearn, etc.).

Additionally, you can use the following functions:
"""

    for tool in tools:
        if isinstance(tool, StructuredTool):
            # Generate signature from args_schema for StructuredTool
            params = []
            if tool.args_schema:
                # Assuming args_schema is a Pydantic model or dict with 'properties'
                if hasattr(tool.args_schema, 'model_json_schema') and callable(tool.args_schema.model_json_schema):
                    schema_props = tool.args_schema.model_json_schema().get('properties', {})
                elif isinstance(tool.args_schema, dict):
                    schema_props = tool.args_schema.get('properties', {})
                else:
                    schema_props = {} # Fallback if schema is not as expected

                for name, prop in schema_props.items():
                    param_type = prop.get('type', 'Any')
                    if 'format' in prop and prop['format'] == 'uri':
                        param_type = 'str' # Special handling for URL format
                    params.append(f"{name}: {param_type}")
            signature = f"({', '.join(params)})"
        else:
            # For regular callables, use inspect.signature
            signature = str(inspect.signature(tool))

        prompt += f'''
def {tool.name}{signature}:
    """{tool.description}"""
    ...
'''

    prompt += """

Variables defined at the top level of previous code snippets can be referenced in your code.

Reminder: use Python code snippets to call tools"""
    return prompt

def eval(code: str, _locals: dict[str, Any]) -> tuple[str, dict[str, Any]]:
    # Store original keys before execution
    original_keys = set(_locals.keys())

    # Pre-import basic scientific packages only
    if 'numpy' not in _locals:
        try:
            import numpy as np
            import pandas as pd
            import matplotlib.pyplot as plt
            _locals.update({
                'np': np, 'numpy': np,
                'pd': pd, 'pandas': pd, 
                'plt': plt, 'matplotlib': plt
            })
        except ImportError:
            pass

    try:
        with contextlib.redirect_stdout(io.StringIO()) as f:
            exec(code, builtins.__dict__, _locals)
        result = f.getvalue()
        if not result:
            result = "<code ran, no output printed to stdout>"
    except Exception as e:
        result = f"Error during execution: {repr(e)}"

    # Capture new variables
    new_vars = {}
    for key, value in _locals.items():
        if (key not in original_keys and 
            not key.startswith('__') and 
            not inspect.ismodule(value)):
            new_vars[key] = value
    
    return result, new_vars


async def build_agent() -> tuple[Any, list]:
    # 1) connect to MCP servers
    client = MultiServerMCPClient(
        {
            "fetch": {"command": "uvx", "args": ["mcp-server-fetch"], "transport": "stdio"},
            "filesystem": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-filesystem", "/workspace", "/app"], "transport": "stdio"},
            # "context7-mcp": {"command": "npx",  "args": [ "-y", "@smithery/cli@latest", "run", "@upstash/context7-mcp",
            #         "--key", "0cf3c1ca-e6aa-4a05-8c1f-35286528a5a7"], "transport": "stdio"}
        }
    )
    mcp_tools = await client.get_tools()
    print(f"MCP Tools: {mcp_tools}")
    # Filter out any None values that might have resulted from failed tool loading
    mcp_tools = [tool for tool in mcp_tools if tool is not None]

    # 2) merge with local tools
    tools = [
        *mcp_tools,
    ]

    # 3) build CodeAct agent as usual
    model = init_chat_model("gemini-2.0-flash", model_provider="google_genai")
    code_act = create_codeact(
        model,
        tools,
        eval,
        prompt=custom_create_default_prompt(
            tools,
            "Once you have the final answer, respond to the user with plain text, do not respond with a code snippet.",
        ),
    )
    agent = code_act.compile(checkpointer=MemorySaver())
    return agent, mcp_tools

# Entrypoint
if __name__ == "__main__":
    agent, _ = asyncio.run(build_agent())

    def stream_from_agent(messages: list[dict], config: RunnableConfig):
        for typ, chunk in agent.stream(
            {"messages": messages},
            stream_mode=["values", "messages"],
            config=config,
        ):
            if typ == "messages":
                print(chunk[0].content, end="")
            elif typ == "values":
                print("\n\n---answer---\n\n", chunk)

    # first turn
    config = {"configurable": {"thread_id": 1}}
    stream_from_agent(
        [
            {
                "role": "user",
                "content": "Decipher this text: 'VGhybCB6dnRsYW9wdW4gZHZ1a2x5bWJz'",
            }
        ],
        config,
    )

    # second turn
    stream_from_agent(
        [
            {
                "role": "user",
                "content": "draw a heart using ASCII'",
            }
        ],
        config,
    )

    # third turn - test context7-mcp
    stream_from_agent(
        [
            {
                "role": "user",
                "content": "use context7, how do I use the requests library in python?",
            }
        ],
        config,
    )

# So other modules can import the graph without running the demo
__all__ = ["build_agent"]
