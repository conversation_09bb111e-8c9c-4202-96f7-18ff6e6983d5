"""
CORRECTED CodeAct A2A Server - Fixed Response Extraction
"""

import asyncio
import uuid
import os
import uvicorn
import httpx
from typing import Any, AsyncIterable, Dict
import logging

logging.basicConfig(level=logging.INFO, 
                   format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

from a2a.server.apps import A2AStarletteApplication
from a2a.server.agent_execution import AgentExecutor, RequestContext
from a2a.server.events.event_queue import EventQueue
from a2a.server.request_handlers import DefaultRequestHandler
from a2a.server.tasks.inmemory_task_store import InMemoryTaskStore
from a2a.server.tasks.inmemory_push_notifier import InMemoryPushNotifier
from a2a.types import (
    AgentCard, AgentCapabilities, AgentSkill, TextPart, Part,
    TaskStatusUpdateEvent, TaskStatus, TaskState,
    TaskArtifactUpdateEvent, Artifact, Task, Role, Message
)

from langchain_core.messages import HumanMessage
from starlette.applications import Starlette
from starlette.routing import Mount, Route
from starlette.responses import JSONResponse, Response

from codeact_graph import build_agent

# Agent Card Setup
SKILL = AgentSkill(
    id="codeact", name="CodeAct", description="Python code execution agent with LangGraph",
    tags=["langgraph", "code", "python"], inputModes=["text/plain"], outputModes=["text/plain"],
)

SERVER_HOST = os.getenv("A2A_SERVER_HOST", "localhost")
SERVER_PORT = int(os.getenv("A2A_SERVER_PORT", "8101"))
PUBLIC_URL = os.getenv("A2A_PUBLIC_URL", f"http://{SERVER_HOST}:{SERVER_PORT}")

AGENT_CARD = AgentCard(
    name="CodeAct Agent",
    description="LangGraph-based Python code execution agent exposed via A2A protocol",
    version="1.0.0", url=f"{PUBLIC_URL}/rpc/", capabilities=AgentCapabilities(streaming=True),
    skills=[SKILL], defaultInputModes=["text/plain"], defaultOutputModes=["text/plain"],
)

class CodeActExecutor(AgentExecutor):
    def __init__(self):
        logger.info("Initializing CodeAct agent...")
        self.graph, self.mcp_tools = asyncio.run(build_agent())
        self.conversation_contexts: Dict[str, Dict[str, Any]] = {}
        logger.info(f"CodeAct agent initialized with {len(self.mcp_tools)} MCP tools")

    async def execute(self, context: RequestContext, event_queue: EventQueue) -> None:
        query = context.get_user_input()
        if not context.message:
            raise Exception('No message provided')

        task_id = context.task_id
        context_id = context.context_id
        
        logger.info(f"Executing task {task_id} with query: {query[:50]}...")

        # Send working status
        event_queue.enqueue_event(
            TaskStatusUpdateEvent(
                contextId=context_id, taskId=task_id,
                status=TaskStatus(
                    state=TaskState.working,
                    message=Message(
                        role=Role.agent,
                        parts=[Part(root=TextPart(text="Processing your code request..."))],
                        messageId=str(uuid.uuid4()),
                    ),
                ), final=False,
            )
        )

        # Execute and get response
        try:
            config = {"configurable": {"thread_id": task_id}}
            final_response = ""
            
            # Run the agent and get the final state
            for typ, chunk in self.graph.stream(
                {"messages": [HumanMessage(content=query)]},
                stream_mode=["values"],
                config=config
            ):
                if typ == "values" and "messages" in chunk:
                    messages = chunk["messages"]
                    if messages:
                        final_response = messages[-1].content

            # Ensure we have a meaningful response
            if not final_response.strip():
                final_response = "Task completed but no output was generated."

            # Send final artifact
            event_queue.enqueue_event(
                TaskArtifactUpdateEvent(
                    contextId=context_id, taskId=task_id,
                    artifact=Artifact(
                        artifactId=str(uuid.uuid4()),
                        parts=[Part(root=TextPart(text=final_response))],
                    ), append=False, lastChunk=True,
                )
            )
            
            # Mark completed
            event_queue.enqueue_event(
                TaskStatusUpdateEvent(
                    contextId=context_id, taskId=task_id,
                    status=TaskStatus(state=TaskState.completed), final=True,
                )
            )
            
            logger.info(f"Task {task_id} completed successfully")

        except Exception as e:
            logger.error(f"Error: {str(e)}", exc_info=True)
            # Send error response
            event_queue.enqueue_event(
                TaskArtifactUpdateEvent(
                    contextId=context_id, taskId=task_id,
                    artifact=Artifact(
                        artifactId=str(uuid.uuid4()),
                        parts=[Part(root=TextPart(text=f"Error: {str(e)}"))],
                    ), append=False, lastChunk=True,
                )
            )
            event_queue.enqueue_event(
                TaskStatusUpdateEvent(
                    contextId=context_id, taskId=task_id,
                    status=TaskStatus(state=TaskState.completed), final=True,
                )
            )

    async def cancel(self, context: RequestContext, event_queue: EventQueue) -> None:
        logger.info(f"Cancelling task {context.task_id}")
        event_queue.enqueue_event(
            TaskStatusUpdateEvent(
                contextId=context.context_id, taskId=context.task_id,
                status=TaskStatus(state=TaskState.canceled), final=True,
            )
        )

def build_app():
    http_handler = DefaultRequestHandler(
        agent_executor=CodeActExecutor(),
        task_store=InMemoryTaskStore(),
        push_notifier=InMemoryPushNotifier(httpx.AsyncClient()),
    )

    a2a_app = A2AStarletteApplication(
        agent_card=AGENT_CARD, http_handler=http_handler,
    ).build()

    routes = [
        Route("/health", lambda r: Response("CodeAct A2A server is running")),
        Route("/.well-known/agent.json", lambda r: JSONResponse(AGENT_CARD.model_dump(mode="json", exclude_none=True))),
        Mount("/rpc", app=a2a_app),
    ]

    return Starlette(routes=routes)

if __name__ == "__main__":
    print(f"Starting CodeAct A2A server at {PUBLIC_URL}/rpc/")
    uvicorn.run(build_app(), host=SERVER_HOST, port=SERVER_PORT, log_level="info")
