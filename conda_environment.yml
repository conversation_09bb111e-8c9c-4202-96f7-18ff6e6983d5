name: codeact
channels:
  - conda-forge
  - defaults
dependencies:
  - python=3.11
  - pip
  - numpy
  - pandas
  - matplotlib
  - seaborn
  - scipy
  - scikit-learn
  - jupyter
  - requests
  - beautifulsoup4
  - lxml
  - openpyxl
  - xlrd
  - pillow
  - opencv
  - ffmpeg
  - imageio
  - plotly
  - bokeh
  - sympy
  - networkx
  - igraph
  - nltk
  - spacy
  - gensim

  # ML libraries via pip for better ARM64 compatibility
  - fastapi
  - aiohttp
  - pytest
  - black
  - flake8
  - mypy
  - ipython
  - jupyterlab
  - git
  - curl
  - wget
  - unzip
  - pip:
    # ML libraries with ARM64 support
    - torch
    - tensorflow
    - transformers
    # Additional pip packages
    - langchain
    - openai
    - anthropic
    - google-generativeai
    - chromadb
    - pinecone-client
    - weaviate-client
    - gradio
    - streamlit
    - dash
    - yfinance
    - alpha-vantage
    - tweepy
    - psutil
    - schedule