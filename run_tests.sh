#!/bin/bash
# Quick test runner for CodeAct agent

echo "🚀 Running comprehensive CodeAct agent tests..."

# Ensure Docker container is running
if ! docker ps | grep -q codeact-agent; then
    echo "Starting CodeAct agent container..."
    docker compose up -d
    sleep 10
fi

# Check if agent is responding
if ! curl -f http://localhost:8101/health > /dev/null 2>&1; then
    echo "❌ Agent not responding. Check container status."
    exit 1
fi

echo "✅ Agent is running, starting tests..."

# Run the comprehensive test suite
cd tests
python comprehensive_test.py

echo "📊 Test complete. Check output above for results."
