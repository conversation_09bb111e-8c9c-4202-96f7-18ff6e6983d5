# Updated for modern Docker Compose - version field is obsolete

services:
  codeact-agent:
    container_name: codeact-agent
    build:
      context: .
      dockerfile: Dockerfile
    ports:
      - "8000:8000"
    env_file:
      - .env
    environment:
      - GOOGLE_API_KEY=${GOOGLE_API_KEY}
      - A2A_SERVER_HOST=0.0.0.0
      - A2A_SERVER_PORT=8000
      - A2A_PATH_PREFIX=/rpc
      - A2A_PUBLIC_URL=http://localhost:8000
    volumes:
      - .:/app:rw
    command: ["python", "codeact_a2a_server.py"]
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 30s
