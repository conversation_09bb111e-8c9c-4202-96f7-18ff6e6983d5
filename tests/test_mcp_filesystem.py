"""
Test MCP filesystem tools in addition to Python file operations
"""

import asyncio
import uuid
import httpx
from a2a.client import A2<PERSON>lient
from a2a.types import MessageSendParams, SendMessageRequest

AGENT_URL = "http://localhost:8101/rpc/"

async def test_mcp_filesystem():
    async with httpx.AsyncClient(follow_redirects=True) as httpx_client:
        client = await A2AClient.get_client_from_agent_card_url(httpx_client, AGENT_URL)
        context_id = str(uuid.uuid4())
        
        print("🧪 Testing package installation")
        
        # Test MCP file creation
        request = SendMessageRequest(params=MessageSendParams(
            message={
                "role": "user",
                "parts": [{"kind": "text", "text": """
# Test package installation capabilities
import subprocess
import sys

# Check current Python version and location
print(f"Python version: {sys.version}")
print(f"Python executable: {sys.executable}")
print(f"Working directory: {sys.path[0]}")

# Test installing a small package
try:
    result = subprocess.run([sys.executable, '-m', 'pip', 'install', 'requests'], 
                          capture_output=True, text=True, timeout=30)
    print(f"Install result: {result.returncode}")
    print(f"Output: {result.stdout[:200]}")
    if result.stderr:
        print(f"Errors: {result.stderr[:200]}")
except Exception as e:
    print(f"Install failed: {e}")

# Test if we can import it
try:
    import requests
    print("✅ Successfully imported requests")
except ImportError as e:
    print(f"❌ Import failed: {e}")
"""}],
                "messageId": str(uuid.uuid4()),
            },
            contextId=context_id
        ))
        
        response = await client.send_message(request)
        
        if hasattr(response.root, 'result') and response.root.result.artifacts:
            part = response.root.result.artifacts[-1].parts[0]
            output = part.root.text if hasattr(part, 'root') else str(part)
            print(f"Output: {output}")
        
        print("✅ Package installation test completed")

if __name__ == "__main__":
    asyncio.run(test_mcp_filesystem())
