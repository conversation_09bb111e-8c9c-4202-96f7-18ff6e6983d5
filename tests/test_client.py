"""
Enhanced test client for the CodeAct A2A server with improved diagnostics.

This script tests both streaming and non-streaming modes of interaction
with your CodeAct agent through the A2A protocol, with better error reporting.
"""

import asyncio
import traceback
import uuid
import json
from typing import Any

import httpx
from a2a.client import A2AClient
from a2a.types import (
    GetTaskRequest,
    GetTaskResponse,
    MessageSendParams,
    Role,
    SendMessageRequest,
    SendMessageResponse,
    SendMessageSuccessResponse,
    SendStreamingMessageRequest,
    Task,
    TaskQueryParams,
    TaskState,
)

# Server configuration - make sure to include the trailing slash
AGENT_URL = "http://localhost:8101/rpc/"
VERBOSE = True  # Set to True for more detailed diagnostic information

def debug_print(message: str, obj: Any = None) -> None:
    """Print debug information if VERBOSE is True."""
    if not VERBOSE:
        return
    
    print(f"\n[DEBUG] {message}")
    if obj is not None:
        if hasattr(obj, "model_dump"):
            print(json.dumps(obj.model_dump(mode="json", exclude_none=True), indent=2))
        elif hasattr(obj, "json"):
            print(obj.json(indent=2))
        elif hasattr(obj, "dict"):
            print(json.dumps(obj.dict(), indent=2))
        else:
            print(obj)
    print()


def create_message_payload(
    text: str, task_id: str | None = None, context_id: str | None = None
) -> dict[str, Any]:
    """Helper to create a message payload for the CodeAct agent."""
    payload: dict[str, Any] = {
        "message": {
            "role": Role.user.value,
            "parts": [{"kind": "text", "text": text}],
            "messageId": str(uuid.uuid4()),
        },
    }

    if task_id:
        # Add task ID directly to the message payload
        payload["message"]["taskId"] = task_id
        if VERBOSE:
            print(f"[DEBUG] Adding task_id to message: {task_id}")

    if context_id:
        # Add context ID directly to the message payload
        payload["message"]["contextId"] = context_id
        if VERBOSE:
            print(f"[DEBUG] Adding context_id to message: {context_id}")
    
    # Also add these to top level if provided (redundant but making sure)
    if task_id:
        payload["taskId"] = task_id
    
    if context_id:
        payload["contextId"] = context_id
    
    debug_print("Created message payload", payload)
    return payload


def print_response(response: Any, description: str) -> None:
    """Print a formatted response from the agent."""
    print(f"\n{'=' * 20} {description} {'=' * 20}")
    if hasattr(response, "root"):
        print(f"{response.root.model_dump_json(exclude_none=True)}\n")
    else:
        print(f"{response.model_dump_json(exclude_none=True)}\n")
    print('=' * (40 + len(description) + 2))


async def test_single_turn(client: A2AClient) -> None:
    """Test a simple single-turn interaction with the agent."""
    print("\n[TEST] Single-turn Python code execution")
    
    # Create a pre-defined task and context ID
    test_task_id = str(uuid.uuid4())
    test_context_id = str(uuid.uuid4())
    
    if VERBOSE:
        print(f"[DEBUG] Using test_task_id: {test_task_id}")
        print(f"[DEBUG] Using test_context_id: {test_context_id}")
    
    # Create a message to send
    payload = create_message_payload(
        text="print('Hello, world!')\nresult = 5 * 10\nprint(f'5 times 10 is {result}')",
        task_id=test_task_id,
        context_id=test_context_id
    )
    request = SendMessageRequest(params=MessageSendParams(**payload))

    try:
        # Send the message and get response
        debug_print("Sending request with payload", payload)
        send_response: SendMessageResponse = await client.send_message(request)
        print_response(send_response, "Code Execution Response")
        debug_print("Received send_response", send_response)

        # Extract the task ID for looking up results
        if not isinstance(send_response.root, SendMessageSuccessResponse):
            print("Error: Received non-success response.")
            if hasattr(send_response.root, "error"):
                print(f"Error details: {send_response.root.error}")
            return

        if not isinstance(send_response.root.result, Task):
            print("Error: Received non-task response.")
            print(f"Response type: {type(send_response.root.result)}")
            return

        task_id: str = send_response.root.result.id
        debug_print(f"Got task ID from response: {task_id}")
        
        # Check if task IDs match
        if task_id != test_task_id:
            print(f"[WARNING] Task ID mismatch: Original={test_task_id}, Response={task_id}")
        
        # Query for task details
        get_request = GetTaskRequest(params=TaskQueryParams(id=task_id))
        debug_print(f"Getting task details for task_id: {task_id}")
        get_response: GetTaskResponse = await client.get_task(get_request)
        
        # Print the full task details
        print_response(get_response, "Task Details")
    except Exception as e:
        print(f"Error in single-turn test: {e}")
        traceback.print_exc()


async def test_streaming(client: A2AClient) -> None:
    """Test streaming interaction with the agent."""
    print("\n[TEST] Streaming Python code execution")
    
    # Create a pre-defined task and context ID for streaming
    stream_task_id = str(uuid.uuid4())
    stream_context_id = str(uuid.uuid4())
    
    if VERBOSE:
        print(f"[DEBUG] Using stream_task_id: {stream_task_id}")
        print(f"[DEBUG] Using stream_context_id: {stream_context_id}")
    
    # Create a message for a more complex task
    payload = create_message_payload(
        text="""
def fibonacci(n):
    if n <= 0:
        return []
    elif n == 1:
        return [0]
    elif n == 2:
        return [0, 1]
    else:
        fib = [0, 1]
        for i in range(2, n):
            fib.append(fib[i-1] + fib[i-2])
        return fib

# Calculate first 10 Fibonacci numbers
result = fibonacci(10)
print(f"First 10 Fibonacci numbers: {result}")
""",
        task_id=stream_task_id,
        context_id=stream_context_id
    )

    try:
        # Create a streaming request
        request = SendStreamingMessageRequest(params=MessageSendParams(**payload))
        debug_print("Sending streaming request with payload", payload)
        
        # Get and print streaming response chunks
        print("\nStreaming chunks:")
        print("----------------")
        stream_response = client.send_message_streaming(request)
        
        chunk_count = 0
        async for chunk in stream_response:
            chunk_count += 1
            print_response(chunk, f"Stream Chunk #{chunk_count}")
            
            # Check chunk for task ID
            if hasattr(chunk, "root") and hasattr(chunk.root, "result"):
                result = chunk.root.result
                if hasattr(result, "taskId"):
                    response_task_id = result.taskId
                    if response_task_id != stream_task_id:
                        print(f"[WARNING] Chunk #{chunk_count} - Task ID mismatch: Original={stream_task_id}, Response={response_task_id}")
        
        print(f"Total chunks received: {chunk_count}")
    except Exception as e:
        print(f"Error in streaming test: {e}")
        traceback.print_exc()


async def test_multi_turn(client: A2AClient) -> None:
    """Test multi-turn interaction with variable persistence."""
    print("\n[TEST] Multi-turn interaction with variable persistence")
    
    # Create pre-defined IDs for multi-turn
    multi_task_id = str(uuid.uuid4())
    multi_context_id = str(uuid.uuid4())
    
    if VERBOSE:
        print(f"[DEBUG] Using multi_task_id: {multi_task_id}")
        print(f"[DEBUG] Using multi_context_id: {multi_context_id}")
    
    try:
        # First turn - define a variable
        first_turn_payload = create_message_payload(
            text="# Define a list of numbers\nnumbers = [1, 2, 3, 4, 5]\nprint(f'Created list: {numbers}')",
            task_id=multi_task_id,
            context_id=multi_context_id
        )
        first_request = SendMessageRequest(params=MessageSendParams(**first_turn_payload))
        debug_print("Sending first turn request with payload", first_turn_payload)
        
        # Send first message
        first_response: SendMessageResponse = await client.send_message(first_request)
        print_response(first_response, "First Turn - Define List")
        debug_print("Received first_response", first_response)

        # Extract context for second turn
        task_id: str | None = None
        
        if isinstance(first_response.root, SendMessageSuccessResponse) and isinstance(
            first_response.root.result, Task
        ):
            task: Task = first_response.root.result
            context_id = task.contextId
            task_id = task.id
            
            debug_print(f"Got task_id={task_id} and context_id={context_id} from first turn")
            
            # Check for task ID mismatch
            if task_id != multi_task_id:
                print(f"[WARNING] Task ID mismatch in first turn: Original={multi_task_id}, Response={task_id}")
            
            # Only proceed if we got a valid context
            if context_id:
                # Wait briefly to ensure processing completes
                await asyncio.sleep(1)
                
                # For second turn, use the task ID from the response
                second_turn_payload = create_message_payload(
                    text="# Calculate the sum of the numbers list\nsum_result = sum(numbers)\nprint(f'Sum of numbers: {sum_result}')",
                    task_id=task_id,
                    context_id=context_id
                )
                debug_print("Sending second turn request with payload", second_turn_payload)
                
                second_request = SendMessageRequest(
                    params=MessageSendParams(**second_turn_payload)
                )
                
                # Send second message
                second_response = await client.send_message(second_request)
                print_response(second_response, "Second Turn - Use Previous Variable")
                debug_print("Received second_response", second_response)
            else:
                print("Warning: Could not get context ID from first turn response.")
        else:
            print("Error: First turn response did not contain a valid task.")
            if hasattr(first_response.root, "error"):
                print(f"Error details: {first_response.root.error}")
    except Exception as e:
        print(f"Error in multi-turn test: {e}")
        traceback.print_exc()


async def main() -> None:
    """Main function to run all tests."""
    print(f"Testing CodeAct agent at {AGENT_URL}")
    
    try:
        # First check if the server is available 
        async with httpx.AsyncClient(follow_redirects=True) as check_client:
            try:
                health_response = await check_client.get(AGENT_URL.rstrip("/") + "/../health")
                if health_response.status_code == 200:
                    print(f"Server health check passed: {health_response.text}")
                else:
                    print(f"Server health check returned status {health_response.status_code}")
            except Exception as e:
                print(f"Health check failed, but we'll still try to connect: {e}")
        
        async with httpx.AsyncClient(follow_redirects=True) as httpx_client:
            # Connect to the agent using the A2A client
            try:
                print("Connecting to agent...")
                agent_card_url = AGENT_URL
                debug_print(f"Using agent_card_url: {agent_card_url}")
                
                client = await A2AClient.get_client_from_agent_card_url(
                    httpx_client, agent_card_url
                )
                print("Successfully connected to CodeAct agent.")
                
                # Run all tests
                await test_single_turn(client)
                await test_streaming(client)
                await test_multi_turn(client)
            except Exception as e:
                print(f"Error connecting to agent: {e}")
                traceback.print_exc()

    except Exception as e:
        traceback.print_exc()
        print(f"Error: {e}")
        print("Make sure the CodeAct A2A server is running.")


if __name__ == "__main__":
    asyncio.run(main())