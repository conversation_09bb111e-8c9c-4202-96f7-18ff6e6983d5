"""
Comprehensive test suite for CodeAct A2A agent running in Docker.
Tests file operations, command execution, and advanced Python capabilities.
"""

import asyncio
import json
import uuid
import httpx
from typing import Any
from a2a.client import A2AClient
from a2a.types import MessageSendParams, SendMessageRequest

AGENT_URL = "http://localhost:8101/rpc/"

class CodeActTester:
    def __init__(self, agent_url: str):
        self.agent_url = agent_url
        self.client = None
        self.httpx_client = None
        self.test_context_id = str(uuid.uuid4())
        
    async def setup(self):
        """Initialize the A2A client."""
        self.httpx_client = httpx.AsyncClient(follow_redirects=True)
        self.client = await A2AClient.get_client_from_agent_card_url(
            self.httpx_client, self.agent_url
        )
        print(f"✅ Connected to CodeAct agent at {self.agent_url}")
        print(f"📋 Using test context: {self.test_context_id}")

    async def cleanup(self):
        """Close the HTTP client."""
        if self.httpx_client:
            await self.httpx_client.aclose()

    async def send_code(self, code: str, description: str = "") -> dict:
        """Send code to the agent and return response."""
        print(f"\n🧪 {description}")
        print(f"Code: {code[:100]}{'...' if len(code) > 100 else ''}")
        
        request = SendMessageRequest(params=MessageSendParams(
            message={
                "role": "user",
                "parts": [{"kind": "text", "text": code}],
                "messageId": str(uuid.uuid4()),
            },
            contextId=self.test_context_id
        ))
        
        response = await self.client.send_message(request)
        
        # Extract output from artifacts
        if hasattr(response.root, 'result') and response.root.result.artifacts:
            part = response.root.result.artifacts[-1].parts[0]
            output = part.root.text if hasattr(part, 'root') else str(part)
            print(f"Output: {output.strip()}")
            return {"success": True, "output": output}
        else:
            print("❌ No output received")
            return {"success": False, "output": ""}

    async def test_file_operations(self):
        """Test file creation, writing, reading, and editing."""
        print("\n" + "="*50)
        print("FILE OPERATIONS TESTS")
        print("="*50)
        
        # Test 1: Create and write file
        await self.send_code("""
# Create a new file and write content
with open('/tmp/test_file.txt', 'w') as f:
    f.write('Hello from CodeAct agent!\\n')
    f.write('Line 2: Testing file operations\\n')
    f.write('Line 3: Docker container test\\n')

print("✅ File created successfully")
""", "Creating a test file")

        # Test 2: Read file content
        await self.send_code("""
# Read the file we just created
with open('/tmp/test_file.txt', 'r') as f:
    content = f.read()

print("File content:")
print(content)
""", "Reading file content")

        # Test 3: Append to file
        await self.send_code("""
# Append new content to the file
with open('/tmp/test_file.txt', 'a') as f:
    f.write('Line 4: Appended content\\n')

# Read updated content
with open('/tmp/test_file.txt', 'r') as f:
    updated_content = f.read()

print("Updated file content:")
print(updated_content)
""", "Appending to file")

        # Test 4: Edit file (replace specific line)
        await self.send_code("""
# Edit file - replace Line 2
with open('/tmp/test_file.txt', 'r') as f:
    lines = f.readlines()

# Replace line 2
lines[1] = 'Line 2: MODIFIED - Testing file operations\\n'

with open('/tmp/test_file.txt', 'w') as f:
    f.writelines(lines)

# Verify the edit
with open('/tmp/test_file.txt', 'r') as f:
    final_content = f.read()

print("File after editing:")
print(final_content)
""", "Editing specific line in file")

    async def test_command_execution(self):
        """Test system command execution."""
        print("\n" + "="*50)
        print("COMMAND EXECUTION TESTS")
        print("="*50)
        
        # Test 1: Basic system commands
        await self.send_code("""
import subprocess
import os

# Test basic commands
result = subprocess.run(['ls', '/tmp'], capture_output=True, text=True)
print("Files in /tmp:")
print(result.stdout)

# Check current working directory
print(f"Current directory: {os.getcwd()}")

# Check Python version
result = subprocess.run(['python', '--version'], capture_output=True, text=True)
print(f"Python version: {result.stdout.strip()}")
""", "Basic system commands")

        # Test 2: File system operations
        await self.send_code("""
import os
import subprocess

# Create directory
os.makedirs('/tmp/codeact_test', exist_ok=True)
print("✅ Created directory /tmp/codeact_test")

# Create multiple files
for i in range(3):
    with open(f'/tmp/codeact_test/file_{i}.txt', 'w') as f:
        f.write(f'Content of file {i}\\n')

# List created files
result = subprocess.run(['ls', '-la', '/tmp/codeact_test'], capture_output=True, text=True)
print("Created files:")
print(result.stdout)
""", "File system operations")

        # Test 3: Process management
        await self.send_code("""
import subprocess

# Check running processes
result = subprocess.run(['ps', 'aux'], capture_output=True, text=True)
lines = result.stdout.split('\\n')[:10]  # First 10 lines
print("Running processes (first 10):")
for line in lines:
    print(line)

# Check system info
result = subprocess.run(['uname', '-a'], capture_output=True, text=True)
print(f"\\nSystem info: {result.stdout.strip()}")
""", "Process and system information")

    async def test_advanced_python(self):
        """Test advanced Python capabilities."""
        print("\n" + "="*50)
        print("ADVANCED PYTHON TESTS")
        print("="*50)
        
        # Test 1: Data processing
        await self.send_code("""
import json
import csv
from datetime import datetime

# Create sample data
data = [
    {"name": "Alice", "age": 30, "city": "New York", "timestamp": "2024-01-01"},
    {"name": "Bob", "age": 25, "city": "San Francisco", "timestamp": "2024-01-02"},
    {"name": "Charlie", "age": 35, "city": "Chicago", "timestamp": "2024-01-03"}
]

# Save as JSON
with open('/tmp/sample_data.json', 'w') as f:
    json.dump(data, f, indent=2)

# Convert to CSV
with open('/tmp/sample_data.csv', 'w', newline='') as f:
    writer = csv.DictWriter(f, fieldnames=data[0].keys())
    writer.writeheader()
    writer.writerows(data)

print("✅ Created JSON and CSV files")
print(f"Processed {len(data)} records")
""", "Data processing and file format conversion")

        # Test 2: Mathematical computations
        await self.send_code("""
import math
import random

# Mathematical computations
def fibonacci(n):
    if n <= 1:
        return n
    return fibonacci(n-1) + fibonacci(n-2)

def prime_factors(n):
    factors = []
    d = 2
    while d * d <= n:
        while n % d == 0:
            factors.append(d)
            n //= d
        d += 1
    if n > 1:
        factors.append(n)
    return factors

# Generate some data
print("Mathematical computations:")
print(f"Fibonacci(10): {fibonacci(10)}")
print(f"Prime factors of 315: {prime_factors(315)}")
print(f"Square root of 2: {math.sqrt(2):.6f}")

# Statistical analysis
random.seed(42)
data = [random.randint(1, 100) for _ in range(10)]
print(f"\\nRandom data: {data}")
print(f"Mean: {sum(data)/len(data):.2f}")
print(f"Min: {min(data)}, Max: {max(data)}")
""", "Mathematical computations and statistics")

    async def test_persistence_across_operations(self):
        """Test that variables persist across different types of operations."""
        print("\n" + "="*50)
        print("PERSISTENCE TESTS")
        print("="*50)
        
        # Test 1: Create persistent data
        await self.send_code("""
# Create various types of data that should persist
project_config = {
    "name": "CodeAct Test Project",
    "version": "1.0.0",
    "files": [],
    "operations": []
}

file_counter = 0
operation_log = []

print("✅ Initialized project configuration")
print(f"Config: {project_config}")
""", "Initialize persistent project state")

        # Test 2: File operations with state tracking
        await self.send_code("""
# Use persistent variables for file operations
for i in range(3):
    filename = f'/tmp/project_file_{file_counter}.txt'
    with open(filename, 'w') as f:
        f.write(f'Project file {file_counter}\\n')
        f.write(f'Created for: {project_config["name"]}\\n')
    
    project_config["files"].append(filename)
    operation_log.append(f"Created {filename}")
    file_counter += 1

print(f"Files created: {len(project_config['files'])}")
print(f"Operations logged: {len(operation_log)}")
print(f"Current file counter: {file_counter}")
""", "File operations with persistent state")

        # Test 3: Final verification
        await self.send_code("""
# Final verification of all persistent data
print("FINAL STATE VERIFICATION")
print("=" * 30)
print(f"Project: {project_config['name']} v{project_config['version']}")
print(f"Files created: {len(project_config['files'])}")
print(f"Total operations: {len(operation_log)}")
print(f"File counter: {file_counter}")

# Verify files still exist
existing_files = []
for filepath in project_config["files"]:
    try:
        with open(filepath, 'r') as f:
            existing_files.append(filepath)
    except FileNotFoundError:
        print(f"❌ File not found: {filepath}")

print(f"\\nVerified existing files: {len(existing_files)}")

# Save final report
with open('/tmp/final_report.json', 'w') as f:
    report = {
        "project_config": project_config,
        "file_counter": file_counter,
        "existing_files": existing_files,
        "total_operations": len(operation_log)
    }
    json.dump(report, f, indent=2)

print("\\n✅ Final report saved to /tmp/final_report.json")
""", "Final verification of persistent state")

    async def run_all_tests(self):
        """Run all test suites."""
        await self.setup()
        
        try:
            await self.test_file_operations()
            await self.test_command_execution()
            await self.test_advanced_python()
            await self.test_persistence_across_operations()
            
            print("\n" + "="*60)
            print("🎉 ALL TESTS COMPLETED SUCCESSFULLY!")
            print("="*60)
            print("✅ File operations: Create, read, write, edit")
            print("✅ Command execution: System commands, process management")
            print("✅ Advanced Python: Data processing, math computations")
            print("✅ Variable persistence: State maintained across operations")
            
        except Exception as e:
            print(f"\n❌ Test failed with error: {e}")
            raise
        finally:
            await self.cleanup()

async def main():
    tester = CodeActTester(AGENT_URL)
    await tester.run_all_tests()

if __name__ == "__main__":
    asyncio.run(main())
