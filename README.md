# CodeAct Agent with A2A Protocol

A LangGraph-based Python code execution agent that implements Google's A2A (Agent-to-Agent) protocol for multi-agent orchestration systems.

## Features

- **Code Execution**: Execute Python code with persistent variable state across conversation turns
- **A2A Protocol**: Full compliance with Google's Agent-to-Agent protocol for multi-agent systems
- **Variable Persistence**: Variables defined in one turn remain available in subsequent turns within the same conversation
- **Streaming Support**: Real-time streaming of code execution results
- **MCP Integration**: Supports tools through Multi-agent Communication Protocol (fetch, web access, etc.)
- **Docker Ready**: Containerized deployment for standalone or multi-agent environments

## Requirements

- Python 3.13+
- Docker (for containerized deployment)
- Google API key for Gemini models

## Quick Start

### Standalone Deployment

1. **Setup environment**:
   ```bash
   cp .env.sample .env
   # Add your GOOGLE_API_KEY to .env
   ```

2. **Run with Docker**:
   ```bash
   docker compose up -d
   ```

3. **Test the agent**:
   ```bash
   curl http://localhost:8000/health
   curl http://localhost:8000/rpc/.well-known/agent.json
   cd tests && python test_client.py
   ```

### Development Mode

1. **Install dependencies**:
   ```bash
   pip install -r requirements.txt
   ```

2. **Run locally**:
   ```bash
   python codeact_a2a_server.py
   ```

## Usage Examples

### A2A Client Integration

```python
import asyncio
from a2a.client import A2AClient
import httpx

async def main():
    async with httpx.AsyncClient() as client:
        agent = await A2AClient.get_client_from_agent_card_url(
            client, "http://localhost:8000/rpc/"
        )
        
        # Send code for execution
        response = await agent.send_message({
            "message": {
                "role": "user",
                "parts": [{"kind": "text", "text": "x = 10\nprint(f'Value: {x}')"}]
            }
        })
        print(response)
```

### Multi-Turn Conversations

The agent maintains variable state across turns in the same conversation:

```python
# Turn 1: Define variables
"data = [1, 2, 3, 4, 5]\nprint(f'Created: {data}')"

# Turn 2: Use previous variables (same context_id)
"total = sum(data)\nprint(f'Sum: {total}')"  # Works! 'data' is available
```

## API Endpoints

- **Health**: `GET /health`
- **Agent Card**: `GET /rpc/.well-known/agent.json`
- **A2A RPC**: `POST /rpc/` (JSON-RPC 2.0)

## Architecture

- **CodeActExecutor**: Handles code execution with variable persistence
- **A2A Integration**: Implements streaming and task management
- **MCP Tools**: Extensible tool system (fetch, web access)
- **Docker**: Production-ready containerization

## Multi-Agent Integration

This agent can be integrated into larger multi-agent systems using the A2A protocol. Each agent maintains its own conversation contexts and can communicate with other A2A-compliant agents.

## Configuration

Environment variables:
- `GOOGLE_API_KEY`: Required for Gemini models
- `A2A_SERVER_HOST`: Server bind address (default: localhost)
- `A2A_SERVER_PORT`: Server port (default: 8000)
- `A2A_PATH_PREFIX`: API path prefix (default: /rpc)

## Testing

Run comprehensive tests including variable persistence:
```bash
cd tests
python test_client.py
```

## License

MIT
