FROM python:3.13-slim

# Set WORKDIR early, useful for relative paths in COPY if any, and standard practice.
WORKDIR /app

# — System dependencies (common tools like curl, git, and unzip for Deno) —
RUN apt-get update && apt-get install -y --no-install-recommends \
        curl ca-certificates git gnupg unzip && \
    rm -rf /var/lib/apt/lists/*

# — uv (and uvx alias, used by MCP client) —
# The astral.sh installer installs 'uv' and 'uvx' to $HOME/.local/bin.
# For the root user in Docker, $HOME is /root.
RUN curl -LsSf https://astral.sh/uv/install.sh | sh
# Add /root/.local/bin to PATH for this and subsequent layers/commands.
# This makes 'uv' and 'uvx' directly executable.
ENV PATH="/root/.local/bin:${PATH}"
# Sanity check: Verify uv and uvx are installed and in PATH.
RUN uv --version && uvx --version

# — Node.js + npx (for context7 MCP) using NodeSource for a specific version —
# Installs Node.js and npm. corepack is then used to enable npx.
RUN curl -fsSL https://deb.nodesource.com/setup_20.x | bash - && \
    apt-get install -y nodejs && \
    corepack enable && \
    node --version && npx --version # Sanity checks for node and npx

# — Deno (as per original Dockerfile's intent) —
# The Deno install script installs to $HOME/.deno/bin (i.e., /root/.deno/bin).
# It requires 'unzip' or '7z' to be installed.
RUN curl -fsSL https://deno.land/install.sh | sh -s -- -y
# Add Deno's bin directory to PATH.
ENV PATH="/root/.deno/bin:${PATH}"
# Sanity check: Verify Deno is installed and in PATH.
RUN deno --version

# — Python application dependencies —
COPY requirements.txt ./
# Install Python packages using pip.
RUN pip install --no-cache-dir -r requirements.txt

# Copy the rest of the application code into the image
COPY . .

# Expose the port the application will listen on (good practice)
EXPOSE 8101

# The CMD is specified in docker-compose.yml. This is a fallback.
CMD ["python", "codeact_a2a_server.py"]