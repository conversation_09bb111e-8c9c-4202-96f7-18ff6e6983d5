FROM python:3.13-slim

WORKDIR /app

# System dependencies
RUN apt-get update && apt-get install -y --no-install-recommends \
        curl ca-certificates git gnupg unzip build-essential && \
    rm -rf /var/lib/apt/lists/*

# Install uv and uvx for MCP tools  
RUN curl -LsSf https://astral.sh/uv/install.sh | sh
ENV PATH="/root/.local/bin:${PATH}"

# Install Node.js for MCP filesystem
RUN curl -fsSL https://deb.nodesource.com/setup_20.x | bash - && \
    apt-get install -y nodejs && \
    corepack enable

# Install miniconda
RUN curl -L -O https://repo.anaconda.com/miniconda/Miniconda3-latest-Linux-x86_64.sh && \
    bash Miniconda3-latest-Linux-x86_64.sh -b -p /opt/miniconda && \
    rm Miniconda3-latest-Linux-x86_64.sh

# Set PATH to include conda and preserve uv/uvx
ENV PATH="/opt/miniconda/bin:/root/.local/bin:${PATH}"

# Create minimal conda environment for user code execution
RUN conda create -n codeact-env python=3.13 numpy pandas matplotlib scipy scikit-learn requests pillow -y

# Install Python dependencies in base environment (for server)
COPY requirements.txt ./
RUN pip install --no-cache-dir -r requirements.txt

# Copy application code
COPY . .

# Create workspace
RUN mkdir -p /workspace

# Expose port
EXPOSE 8101

CMD ["python", "codeact_a2a_server.py"]