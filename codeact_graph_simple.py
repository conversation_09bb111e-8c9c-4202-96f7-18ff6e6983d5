import asyncio
from typing import Any

from langchain.chat_models import init_chat_model
from langchain_core.runnables import RunnableConfig
from langgraph.checkpoint.memory import MemorySaver
from langgraph_codeact import create_codeact
from dotenv import load_dotenv
load_dotenv()

def eval(code: str, _locals: dict[str, Any]) -> tuple[str, dict[str, Any]]:
    import builtins
    import contextlib
    import io
    import inspect
    
    # Store original keys before execution
    original_keys = set(_locals.keys())

    # Pre-import basic scientific packages only
    if 'numpy' not in _locals:
        try:
            import numpy as np
            import pandas as pd
            import matplotlib.pyplot as plt
            _locals.update({
                'np': np, 'numpy': np,
                'pd': pd, 'pandas': pd, 
                'plt': plt, 'matplotlib': plt
            })
        except ImportError:
            pass

    try:
        with contextlib.redirect_stdout(io.StringIO()) as f:
            exec(code, builtins.__dict__, _locals)
        result = f.getvalue()
        if not result:
            result = "<code ran, no output printed to stdout>"
    except Exception as e:
        result = f"Error during execution: {repr(e)}"

    # Capture new variables
    new_vars = {}
    for key, value in _locals.items():
        if (key not in original_keys and 
            not key.startswith('__') and 
            not inspect.ismodule(value)):
            new_vars[key] = value
    
    return result, new_vars

async def build_agent() -> tuple[Any, list]:
    # Skip MCP for now - just basic tools
    tools = []
    
    model = init_chat_model("gemini-2.0-flash", model_provider="google_genai")
    code_act = create_codeact(model, tools, eval)
    agent = code_act.compile(checkpointer=MemorySaver())
    return agent, tools

__all__ = ["build_agent"]