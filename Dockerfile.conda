FROM continuumio/miniconda3:latest

# Set WORKDIR
WORKDIR /app

# System dependencies for MCP tools and A2A
RUN apt-get update && apt-get install -y --no-install-recommends \
    curl ca-certificates git gnupg unzip build-essential \
    && rm -rf /var/lib/apt/lists/*

# Install uv for MCP client
RUN curl -LsSf https://astral.sh/uv/install.sh | sh
ENV PATH="/root/.local/bin:${PATH}"

# Install Node.js for MCP servers
RUN curl -fsSL https://deb.nodesource.com/setup_20.x | bash - && \
    apt-get install -y nodejs && \
    corepack enable

# Install Deno for additional MCP support
RUN curl -fsSL https://deno.land/install.sh | sh -s -- -y
ENV PATH="/root/.deno/bin:${PATH}"

# Create conda environment for code execution
COPY conda_environment.yml ./
RUN conda env create -f conda_environment.yml

# Activate environment by default for all RUN commands
SHELL ["conda", "run", "-n", "codeact", "/bin/bash", "-c"]

# Install Python dependencies for the agent itself (not in conda env)
COPY requirements.txt ./
RUN pip install --no-cache-dir -r requirements.txt

# Copy application code
COPY . .

# Create workspace directory that will be accessible to the conda environment
RUN mkdir -p /workspace /app/data/conversations
RUN chown -R root:root /workspace

# Set environment variables for conda execution
ENV CONDA_DEFAULT_ENV=codeact
ENV CODEACT_CONDA_ENV_PATH=/opt/conda/envs/codeact
ENV CODEACT_MCP_FILESYSTEM_PATH=/workspace
ENV CODEACT_MCP_TMP_PATH=/tmp

# Expose port
EXPOSE 8101

CMD ["python", "codeact_a2a_server.py"]